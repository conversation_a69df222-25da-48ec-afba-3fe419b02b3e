"use client";

import { useEffect, useState } from 'react';
import { motion, useSpring } from 'framer-motion';

export default function CustomCursor({ mousePosition }: { mousePosition?: { x: number, y: number } } = {}) {
  const [isVisible, setIsVisible] = useState(false);
  const cursorX = useSpring(0, { damping: 20, stiffness: 300, mass: 0.5 });
  const cursorY = useSpring(0, { damping: 20, stiffness: 300, mass: 0.5 });
  const dotX = useSpring(0, { damping: 30, stiffness: 400, mass: 0.2 });
  const dotY = useSpring(0, { damping: 30, stiffness: 400, mass: 0.2 });

  useEffect(() => {
    const moveCursor = (e: MouseEvent) => {
      cursorX.set(e.clientX - 16);
      cursorY.set(e.clientY - 16);
      dotX.set(e.clientX - 2);
      dotY.set(e.clientY - 2);
    };

    const handleMouseEnter = () => setIsVisible(true);
    const handleMouseLeave = () => setIsVisible(false);

    window.addEventListener('mousemove', moveCursor);
    window.addEventListener('mouseenter', handleMouseEnter);
    window.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      window.removeEventListener('mousemove', moveCursor);
      window.removeEventListener('mouseenter', handleMouseEnter);
      window.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [cursorX, cursorY, dotX, dotY]);

  if (typeof window === "undefined") return null;

  return (
    <>
      {/* Small dot */}
      <motion.div
        style={{
          x: dotX,
          y: dotY,
        }}
        animate={{
          opacity: isVisible ? 1 : 0,
          scale: isVisible ? 1 : 0,
        }}
        className="fixed top-0 left-0 w-2 h-2 bg-gray-100 rounded-full pointer-events-none z-50"
        transition={{ duration: 0.2 }}
      />
      {/* Large circle */}
      <motion.div
        className="fixed top-0 left-0 w-8 h-8 rounded-full pointer-events-none z-50 bg-white/10 border-2 border-gray-100"
        style={{
          x: cursorX,
          y: cursorY,
        }}
        animate={{
          opacity: isVisible ? 1 : 0,
          scale: isVisible ? 1 : 0,
        }}
        transition={{ duration: 0.2 }}
      />
    </>
  );
}
