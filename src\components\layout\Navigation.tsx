"use client";

import { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { usePathname } from 'next/navigation';

const navItems = [
  { href: '/', label: 'Home' },
  { href: '/projects', label: 'Projects' },
  { href: '/about', label: 'About' },
  { href: '/contact', label: 'Contact' },
];

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const pathname = usePathname();

  return (
    <header className="fixed w-full top-0 z-40 backdrop-blur-sm bg-black/50">
      <nav className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link href="/" className="relative group">
            <motion.span
              className="text-xl font-bold gradient-text"
              whileHover={{
                scale: 1.1,
                transition: { duration: 0.2, ease: "easeOut" }
              }}
              whileTap={{ scale: 0.95 }}
            >
              Jaeylo
            </motion.span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item, index) => {
              const isActive = pathname === item.href;
              return (
                <motion.div
                  key={item.href}
                  onHoverStart={() => setHoveredIndex(index)}
                  onHoverEnd={() => setHoveredIndex(null)}
                  className="relative"
                >
                  <Link
                    href={item.href}
                    className="relative group py-2 px-4"
                  >
                    <motion.span
                      className={`relative z-10 transition-colors duration-200 ${
                        isActive 
                          ? 'gradient-text font-semibold' 
                          : 'text-white/80 hover:gradient-text'
                      }`}
                      animate={{
                        y: hoveredIndex === index ? -2 : 0
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      {item.label}
                    </motion.span>

                    {/* Underline effect - change back to the line instead of background */}
                    <motion.span
                      className="absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-[#facc15] via-[#ef4444] to-[#9333ea]"
                      initial={{ width: "0%" }}
                      animate={{
                        width: hoveredIndex === index || isActive ? "100%" : "0%"
                      }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                    />
                  </Link>
                </motion.div>
              );
            })}
          </div>

          {/* Mobile menu button */}
          <motion.button
            className="md:hidden text-white p-2"
            onClick={() => setIsOpen(!isOpen)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <span className="sr-only">Open menu</span>
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <motion.path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                animate={{
                  d: isOpen
                    ? "M6 18L18 6M6 6l12 12"
                    : "M4 6h16M4 12h16M4 18h16"
                }}
                transition={{ duration: 0.3 }}
              />
            </svg>
          </motion.button>
        </div>

        {/* Mobile menu */}
        <motion.div
          className="md:hidden"
          initial={{ height: 0, opacity: 0 }}
          animate={{
            height: isOpen ? "auto" : 0,
            opacity: isOpen ? 1 : 0
          }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <div className="py-4 space-y-2">
            {navItems.map((item) => (
              <motion.div
                key={item.href}
                whileHover={{ x: 10 }}
                transition={{ duration: 0.2 }}
              >
                <Link
                  href={item.href}
                  className={`block py-2 ${
                    pathname === item.href 
                      ? 'gradient-text font-semibold' 
                      : 'text-white/80 hover:gradient-text'
                  }`}
                  onClick={() => setIsOpen(false)}
                >
                  {item.label}
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </nav>
    </header>
  );
}
