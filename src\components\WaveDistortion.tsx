"use client";

import { useEffect, useRef } from 'react';
import * as THREE from 'three';

const vertexShader = `
  varying vec2 vUv;
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`;

const fragmentShader = `
  uniform float time;
  uniform sampler2D texture1;
  varying vec2 vUv;

  vec3 primaryColor = vec3(0.176, 0.353, 0.153);  // #2d5a27
  vec3 accentColor = vec3(0.831, 0.686, 0.216);   // #d4af37

  void main() {
    vec2 uv = vUv;

    // Create wave distortion
    float frequency = 10.0;
    float amplitude = 0.005;
    float x = uv.x + amplitude * sin(frequency * uv.y + time);
    float y = uv.y + amplitude * sin(frequency * uv.x + time);

    // Add secondary wave with accent color influence
    x += amplitude * 0.5 * sin(frequency * 0.5 * uv.y + time * 0.5);
    y += amplitude * 0.5 * sin(frequency * 0.5 * uv.x + time * 0.5);

    vec2 distortedUV = vec2(x, y);
    vec4 texture = texture2D(texture1, distortedUV);

    // Add color tint
    float tintStrength = 0.2;
    vec3 tintColor = mix(primaryColor, accentColor, sin(time * 0.5) * 0.5 + 0.5);
    vec3 finalColor = mix(texture.rgb, tintColor, tintStrength);

    gl_FragColor = vec4(finalColor, texture.a);
  }
`;

interface WaveDistortionProps {
  imageUrl: string;
  className?: string;
}

export default function WaveDistortion({ imageUrl, className = '' }: WaveDistortionProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.OrthographicCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const materialRef = useRef<THREE.ShaderMaterial | null>(null);
  const timeRef = useRef<number>(0);
  const frameRef = useRef<number>(0);

  useEffect(() => {
    if (!containerRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Orthographic camera for 2D rendering
    const camera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);
    cameraRef.current = camera;

    // WebGL renderer
    const renderer = new THREE.WebGLRenderer({ alpha: true });
    renderer.setPixelRatio(window.devicePixelRatio);
    rendererRef.current = renderer;

    // Load texture
    const textureLoader = new THREE.TextureLoader();
    textureLoader.load(imageUrl, (texture) => {
      // Create shader material
      const material = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 },
          texture1: { value: texture }
        },
        vertexShader,
        fragmentShader,
      });
      materialRef.current = material;

      // Create plane geometry
      const geometry = new THREE.PlaneGeometry(2, 2);
      const mesh = new THREE.Mesh(geometry, material);
      scene.add(mesh);

      // Set renderer size
      const updateSize = () => {
        if (!containerRef.current || !renderer) return;
        const { width, height } = containerRef.current.getBoundingClientRect();
        renderer.setSize(width, height);
      };

      // Initial size
      updateSize();
      if (containerRef.current) {
        containerRef.current.appendChild(renderer.domElement);
      }

      // Handle resize
      window.addEventListener('resize', updateSize);

      // Animation loop
      const animate = () => {
        frameRef.current = requestAnimationFrame(animate);
        if (materialRef.current) {
          timeRef.current += 0.01;
          materialRef.current.uniforms.time.value = timeRef.current;
        }
        renderer.render(scene, camera);
      };
      animate();

      // Cleanup
      return () => {
        window.removeEventListener('resize', updateSize);
        cancelAnimationFrame(frameRef.current);
        renderer.dispose();
        geometry.dispose();
        material.dispose();
        texture.dispose();
      };
    });

    return () => {
      if (rendererRef.current) {
        rendererRef.current.dispose();
      }
    };
  }, [imageUrl]);

  return (
    <div ref={containerRef} className={`w-full h-full ${className}`} />
  );
}
