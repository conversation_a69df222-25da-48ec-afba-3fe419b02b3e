"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import BlobImage from './BlobImage';

const roles = [
  "Frontend Developer",
  "UI/UX Designer",
  "Creative Coder"
];

export default function Hero() {
  const [currentRole, setCurrentRole] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const title = "Hi, I'm <PERSON>".split("");

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    const role = roles[currentRole];
    const updateText = () => {
      if (!isDeleting) {
        if (displayText.length < role.length) {
          setDisplayText(role.slice(0, displayText.length + 1));
          timeout = setTimeout(updateText, 100);
        } else {
          timeout = setTimeout(() => setIsDeleting(true), 2000);
        }
      } else {
        if (displayText.length > 0) {
          setDisplayText(role.slice(0, displayText.length - 1));
          timeout = setTimeout(updateText, 50);
        } else {
          setIsDeleting(false);
          setCurrentRole((prev) => (prev + 1) % roles.length);
        }
      }
    };

    timeout = setTimeout(updateText, 100);
    return () => clearTimeout(timeout);
  }, [currentRole, displayText, isDeleting]);

  return (
    <div className="min-h-screen relative flex items-center justify-center overflow-hidden">
      {/* Background gradient updated to match the first snippet */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#ff8a00]/10 to-transparent pointer-events-none" />
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8 items-center">
          <div>
            <div className="mb-6">
              {/* Animated title */}
              <div className="flex flex-wrap gap-1 text-4xl md:text-5xl font-bold mb-4">
                {title.map((letter, index) => (
                  <motion.span
                    key={index}
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{
                      duration: 0.5,
                      delay: index * 0.1,
                      type: "spring",
                      stiffness: 200,
                      damping: 15
                    }}
                    className="gradient-text"
                  >
                    {letter === " " ? "\u00A0" : letter}
                  </motion.span>
                ))}
              </div>
              
              {/* Typing effect */}
              <div className="h-8 flex items-center">
                <motion.span 
                  className="text-xl md:text-2xl font-medium bg-gradient-to-r from-yellow-500 via-orange-500 to-pink-600 bg-clip-text text-transparent"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.5 }}
                >
                  {displayText}
                  <span className="animate-pulse">|</span>
                </motion.span>
              </div>
            </div>
            
            <motion.p 
              className="text-lg text-foreground/80 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2 }}
            >
              I create engaging digital experiences with clean code and creative design.
              Let's build something amazing together.
            </motion.p>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2.5 }}
              className="flex flex-wrap gap-4"
            >
              <a 
                href="/contact" 
                className="px-6 py-3 rounded-full bg-gradient-to-r from-yellow-500 via-orange-500 to-pink-600 text-white font-medium hover:shadow-lg hover:shadow-pink-500/20 transition-all"
              >
                Get in Touch
              </a>
              <a 
                href="/projects" 
                className="px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white font-medium hover:bg-white/20 transition-all"
              >
                View Projects
              </a>
            </motion.div>
          </div>
          
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ 
              delay: 0.5,
              duration: 0.8,
              type: "spring",
              stiffness: 100
            }}
            className="hidden md:block"
          >
            <div className="relative">
              <BlobImage src="/profile.jpg" alt="Jackson's profile" width={400} height={400} />
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
