"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";

const posts = [
  {
    title: "Building Scalable Web Applications",
    excerpt: "Learn the best practices for creating scalable and maintainable web applications using modern technologies.",
    image: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=600&fit=crop",
    date: "2024-03-15",
    readTime: "5 min read",
    category: "Development",
    slug: "building-scalable-web-applications",
  },
  {
    title: "UI/UX Design Principles",
    excerpt: "Explore key principles of UI/UX design that can help create better user experiences.",
    image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&h=600&fit=crop",
    date: "2024-03-10",
    readTime: "4 min read",
    category: "Design",
    slug: "ui-ux-design-principles",
  },
  {
    title: "The Future of Web Development",
    excerpt: "Discover emerging trends and technologies shaping the future of web development.",
    image: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=600&fit=crop",
    date: "2024-03-05",
    readTime: "6 min read",
    category: "Technology",
    slug: "future-of-web-development",
  },
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export default function BlogPosts() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <motion.div
        className="text-center mb-12"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
      >
        <h2 className="text-3xl sm:text-4xl font-bold mb-4 gradient-text">Latest Blog Posts</h2>
        <p className="text-foreground/80">Insights and tutorials about web development and design</p>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {posts.map((post) => (
          <motion.article key={post.title} variants={itemVariants} className="group">
            <Link href={`/blog/${post.slug}`} className="block">
              <div className="relative h-48 mb-4 rounded-xl overflow-hidden">
                <Image
                  src={post.image}
                  alt={post.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-4 text-sm">
                  <span className="text-accent">{post.category}</span>
                  <span className="text-foreground/60">•</span>
                  <span className="text-foreground/60">{post.readTime}</span>
                  <span className="text-foreground/60">•</span>
                  <span className="text-foreground/60">{new Date(post.date).toLocaleDateString()}</span>
                </div>
                <h3 className="font-bold text-xl group-hover:text-accent transition-colors">
                  {post.title}
                </h3>
                <p className="text-foreground/80">{post.excerpt}</p>
              </div>
            </Link>
          </motion.article>
        ))}
      </motion.div>

      <motion.div
        className="text-center mt-12"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
      >
        <Link
          href="/blog"
          className="inline-flex items-center gap-2 text-accent hover:text-accent-light group transition-colors"
        >
          View all posts
          <motion.svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            initial={{ x: 0 }}
            animate={{ x: 3 }}
            transition={{
              repeat: Infinity,
              repeatType: "reverse",
              duration: 1,
            }}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </motion.svg>
        </Link>
      </motion.div>
    </div>
  );
}