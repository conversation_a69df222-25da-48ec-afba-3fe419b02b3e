"use client";

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

// Sample blog data
const blogs = [
  {
    id: 1,
    title: "The Future of Web Development: Trends to Watch in 2023",
    excerpt: "Explore the emerging technologies and methodologies that are shaping the future of web development.",
    image: "/blogs/blog-1.jpg",
    date: "June 15, 2023",
    category: "Web Development",
    readTime: "5 min read"
  },
  {
    id: 2,
    title: "Optimizing React Applications for Performance",
    excerpt: "Learn practical techniques to improve the performance of your React applications and deliver better user experiences.",
    image: "/blogs/blog-2.jpg",
    date: "July 22, 2023",
    category: "React",
    readTime: "8 min read"
  },
  {
    id: 3,
    title: "Designing Accessible User Interfaces",
    excerpt: "A comprehensive guide to creating web interfaces that are accessible to users with disabilities.",
    image: "/blogs/blog-3.jpg",
    date: "August 10, 2023",
    category: "UI/UX Design",
    readTime: "6 min read"
  }
];

export default function LatestBlogs() {
  return (
    <div className="py-24 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute bottom-0 left-0 w-full h-64 bg-gradient-to-t from-[#d946ef]/10 to-transparent pointer-events-none" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ y: 30, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          viewport={{ once: true }}
          transition={{ type: "spring", stiffness: 100 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 gradient-text">Latest Articles</h2>
          <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
            Insights, tutorials, and thoughts on web development, design, and technology.
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogs.map((blog, index) => (
            <motion.article
              key={blog.id}
              className="bg-white/5 backdrop-blur-sm rounded-xl overflow-hidden border border-white/10 hover:border-white/20 transition-all"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
              whileHover={{ y: -5 }}
            >
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={blog.image}
                  alt={blog.title}
                  fill
                  className="object-cover transition-transform duration-500 hover:scale-105"
                />
                <div className="absolute top-4 left-4 px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-xs font-medium">
                  {blog.category}
                </div>
              </div>
              
              <div className="p-6">
                <div className="flex items-center text-sm text-foreground/60 mb-3">
                  <span>{blog.date}</span>
                  <span className="mx-2">•</span>
                  <span>{blog.readTime}</span>
                </div>
                
                <h3 className="text-xl font-bold mb-3 line-clamp-2">
                  <Link href={`/blog/${blog.id}`} className="hover:text-[#d946ef] transition-colors">
                    {blog.title}
                  </Link>
                </h3>
                
                <p className="text-foreground/70 mb-4 line-clamp-3">
                  {blog.excerpt}
                </p>
                
                <Link 
                  href={`/blog/${blog.id}`}
                  className="inline-flex items-center text-sm font-medium gradient-text hover:underline"
                >
                  Read More
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>
              </div>
            </motion.article>
          ))}
        </div>
        
        <motion.div 
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
        >
          <Link 
            href="/blog" 
            className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm text-white font-medium hover:bg-white/20 transition-all"
          >
            View All Articles
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </motion.div>
      </div>
    </div>
  );
}