"use client";

import { motion } from 'framer-motion';

interface ProjectFilterProps {
  categories: string[];
  selectedCategory: string;
  onSelectCategory: (category: string) => void;
}

export default function ProjectFilter({ 
  categories, 
  selectedCategory, 
  onSelectCategory 
}: ProjectFilterProps) {
  return (
    <div className="flex flex-wrap justify-center gap-4">
      {categories.map((category, index) => (
        <motion.button
          key={category}
          onClick={() => onSelectCategory(category)}
          className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
            selectedCategory === category 
              ? 'bg-gradient-to-r from-[#ff8a00] to-[#d946ef] text-white shadow-lg shadow-[#d946ef]/20' 
              : 'bg-white/10 backdrop-blur-sm text-foreground/80 hover:bg-white/20'
          }`}
          whileHover={{ y: -3 }}
          whileTap={{ scale: 0.95 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ 
            opacity: 1, 
            y: 0,
            transition: { 
              delay: 0.1 * index,
              duration: 0.5,
              type: "spring",
              stiffness: 100
            }
          }}
        >
          {category}
        </motion.button>
      ))}
    </div>
  );
}