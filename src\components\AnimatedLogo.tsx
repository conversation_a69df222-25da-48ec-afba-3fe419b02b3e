"use client";

import { motion } from 'framer-motion';

export default function AnimatedLogo() {
  return (
    <motion.div 
      className="flex items-center cursor-pointer relative group"
      whileHover="hover"
      whileTap="tap"
      initial="initial"
    >
      {/* Gradient background that appears on hover */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-lg -z-10"
        variants={{
          initial: { opacity: 0, scale: 0.8 },
          hover: { 
            opacity: 1, 
            scale: 1.2,
            transition: { duration: 0.3 }
          },
          tap: { scale: 0.95 }
        }}
      />

      {/* Letters container */}
      <div className="px-3 py-1">
        <motion.span 
          className="text-2xl font-bold inline-block text-primary"
          variants={{
            initial: { x: 0, rotate: 0 },
            hover: {
              x: -8,
              rotate: -8,
              transition: { 
                type: "spring",
                stiffness: 400,
                damping: 17
              }
            },
            tap: { scale: 0.9 }
          }}
        >
          J
        </motion.span>
        <motion.span 
          className="text-2xl font-bold inline-block text-accent"
          variants={{
            initial: { x: 0, rotate: 0 },
            hover: {
              x: 8,
              rotate: 8,
              transition: { 
                type: "spring",
                stiffness: 400,
                damping: 17
              }
            },
            tap: { scale: 0.9 }
          }}
        >
          L
        </motion.span>
        <motion.span 
          className="text-2xl inline-block gradient-text"
          variants={{
            initial: { scale: 1, y: 0 },
            hover: {
              scale: 1.2,
              y: -2,
              transition: { 
                type: "spring",
                stiffness: 400,
                damping: 10
              }
            },
            tap: { 
              scale: 0.8,
              transition: { duration: 0.1 }
            }
          }}
        >
          .
        </motion.span>
      </div>

      {/* Subtle particle effects on hover */}
      <motion.div
        className="absolute -right-1 top-0"
        variants={{
          initial: { opacity: 0 },
          hover: {
            opacity: [0, 1, 0],
            transition: {
              duration: 1.5,
              repeat: Infinity,
              repeatType: "reverse"
            }
          }
        }}
      >
        <div className="w-1 h-1 rounded-full bg-primary" />
      </motion.div>
      <motion.div
        className="absolute -left-1 bottom-0"
        variants={{
          initial: { opacity: 0 },
          hover: {
            opacity: [0, 1, 0],
            transition: {
              duration: 1.2,
              delay: 0.3,
              repeat: Infinity,
              repeatType: "reverse"
            }
          }
        }}
      >
        <div className="w-1 h-1 rounded-full bg-accent" />
      </motion.div>
    </motion.div>
  );
}
