{"name": "my-website-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "pre-deploy": "node pre-deploy.js", "vercel-build": "node pre-deploy.js && next build"}, "dependencies": {"@types/nodemailer": "^6.4.17", "@types/three": "^0.173.0", "contentful": "^11.5.0", "framer-motion": "^12.4.5", "next": "^15.2.4", "nodemailer": "^6.10.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-error-boundary": "^5.0.0", "react-icons": "^5.5.0", "resend": "^4.2.0", "three": "^0.173.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "^15.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}