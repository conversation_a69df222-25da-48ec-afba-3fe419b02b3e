# PowerShell script to kill processes using common development ports

$ports = @(3000, 3001, 3002, 3003, 3004, 3005, 3006, 3007, 3008, 3009, 3010, 4000, 4200, 5000, 5001, 5173, 8000, 8080, 8888, 9000)

Write-Host "Scanning for processes using common development ports..."

$foundProcesses = $false

foreach ($port in $ports) {
    $connections = Get-NetTCPConnection -LocalPort $port -State Listen -ErrorAction SilentlyContinue
    
    if ($connections) {
        foreach ($conn in $connections) {
            $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
            
            if ($process) {
                $foundProcesses = $true
                Write-Host "Found process: $($process.ProcessName) (ID: $($process.Id)) using port $port"
                Write-Host "Terminating process..."
                Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
                
                if ($?) {
                    Write-Host "Successfully terminated process on port $port" -ForegroundColor Green
                } else {
                    Write-Host "Failed to terminate process on port $port" -ForegroundColor Red
                }
            }
        }
    }
}

if (-not $foundProcesses) {
    Write-Host "No processes found using common development ports." -ForegroundColor Yellow
} else {
    Write-Host "Port cleanup completed." -ForegroundColor Green
}