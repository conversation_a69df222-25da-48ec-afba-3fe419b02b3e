"use client";

import { useState } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';

// Sample testimonial data
const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Marketing Director, TechCorp",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face",
    content: "Working with <PERSON> was an absolute pleasure. His attention to detail and creative approach to problem-solving resulted in a website that exceeded our expectations. Highly recommended!",
    rating: 5
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Founder, StartupHub",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
    content: "<PERSON> delivered our project on time and on budget. His technical skills are impressive, but what really sets him apart is his ability to understand our business needs and translate them into effective solutions.",
    rating: 5
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "E-commerce Manager, StyleBoutique",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face",
    content: "Our online store's conversion rate increased by 40% after <PERSON> redesigned our website. His understanding of UX principles and attention to performance optimization made all the difference.",
    rating: 5
  },
  {
    id: 4,
    name: "David Okafor",
    role: "CTO, FinTech Solutions",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face",
    content: "Jackson's technical expertise is outstanding. He helped us implement complex features while maintaining clean, maintainable code. A true professional who delivers quality work.",
    rating: 5
  }
];

export default function Testimonials() {
  const [activeIndex, setActiveIndex] = useState(0);

  return (
    <div className="py-24 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute top-0 right-0 w-full h-64 bg-gradient-to-b from-[#ff8a00]/10 to-transparent pointer-events-none" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ y: 30, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          viewport={{ once: true }}
          transition={{ type: "spring", stiffness: 100 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 gradient-text">Client Testimonials</h2>
          <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
            Don't just take my word for it. Here's what my clients have to say about working with me.
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Testimonial Showcase */}
          <motion.div
            className="relative h-[400px] bg-white/5 backdrop-blur-sm rounded-xl overflow-hidden border border-white/10"
            initial={{ x: -50, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ type: "spring", stiffness: 100 }}
          >
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent z-10" />
            
            <Image
              src={testimonials[activeIndex].image}
              alt={testimonials[activeIndex].name}
              fill
              className="object-cover transition-transform duration-700"
              style={{ transform: `scale(1.05)` }}
            />
            
            <div className="absolute bottom-0 left-0 right-0 p-8 z-20">
              <div className="flex mb-3">
                {[...Array(testimonials[activeIndex].rating)].map((_, i) => (
                  <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              
              <p className="text-white text-lg italic mb-4">"{testimonials[activeIndex].content}"</p>
              
              <div className="flex items-center">
                <div className="h-12 w-12 rounded-full overflow-hidden border-2 border-white/50 mr-4">
                  <Image
                    src={testimonials[activeIndex].image}
                    alt={testimonials[activeIndex].name}
                    width={48}
                    height={48}
                    className="object-cover"
                  />
                </div>
                <div>
                  <h4 className="text-white font-semibold">{testimonials[activeIndex].name}</h4>
                  <p className="text-white/70 text-sm">{testimonials[activeIndex].role}</p>
                </div>
              </div>
            </div>
          </motion.div>
          
          {/* Testimonial List */}
          <motion.div
            className="space-y-4"
            initial={{ x: 50, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ type: "spring", stiffness: 100 }}
          >
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                className={`p-4 rounded-lg cursor-pointer transition-all ${
                  activeIndex === index 
                    ? 'bg-gradient-to-r from-[#ff8a00]/20 to-[#d946ef]/20 border border-white/10' 
                    : 'bg-white/5 hover:bg-white/10'
                }`}
                onClick={() => setActiveIndex(index)}
                whileHover={{ x: 5 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center">
                  <div className="h-12 w-12 rounded-full overflow-hidden mr-4">
                    <Image
                      src={testimonial.image}
                      alt={testimonial.name}
                      width={48}
                      height={48}
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <h4 className="font-medium">{testimonial.name}</h4>
                    <p className="text-sm text-foreground/70">{testimonial.role}</p>
                  </div>
                </div>
                <p className="mt-2 text-sm line-clamp-2">{testimonial.content}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  );
}