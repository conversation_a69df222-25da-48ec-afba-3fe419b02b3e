"use client";

import { motion } from 'framer-motion';

interface WaveImageProps {
  src: string;
  alt?: string;
  className?: string;
}

export default function WaveImage({ src, alt = "", className = "" }: WaveImageProps) {
  return (
    <div className={`relative overflow-hidden ${className}`}>
      <motion.div
        className="relative w-full h-full"
        animate={{
          filter: [
            'url(#wave-1)',
            'url(#wave-2)',
            'url(#wave-3)',
            'url(#wave-2)',
            'url(#wave-1)'
          ]
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        style={{
          width: '100%',
          height: '100%',
          position: 'relative'
        }}
      >
        <img
          src={src}
          alt={alt}
          className="w-full h-full object-cover"
        />

        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 to-accent/20 mix-blend-overlay" />
      </motion.div>

      {/* SVG Filters */}
      <svg width="0" height="0" className="absolute">
        <defs>
          <filter id="wave-1">
            <feTurbulence type="fractalNoise" baseFrequency="0.02 0.05" numOctaves="2" result="noise" />
            <feColorMatrix
              type="matrix"
              values="1 0 0 0 0
                      0 1 0 0 0
                      0 0 1 0 0
                      0 0 0 5 -2"
              result="colorNoise"
            />
            <feComposite operator="in" in2="SourceGraphic" />
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="20" />
          </filter>
          <filter id="wave-2">
            <feTurbulence type="fractalNoise" baseFrequency="0.02 0.06" numOctaves="2" result="noise" />
            <feColorMatrix
              type="matrix"
              values="1 0 0 0 0
                      0 1 0 0 0
                      0 0 1 0 0
                      0 0 0 5 -2"
              result="colorNoise"
            />
            <feComposite operator="in" in2="SourceGraphic" />
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="30" />
          </filter>
          <filter id="wave-3">
            <feTurbulence type="fractalNoise" baseFrequency="0.02 0.04" numOctaves="2" result="noise" />
            <feColorMatrix
              type="matrix"
              values="1 0 0 0 0
                      0 1 0 0 0
                      0 0 1 0 0
                      0 0 0 5 -2"
              result="colorNoise"
            />
            <feComposite operator="in" in2="SourceGraphic" />
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="25" />
          </filter>
        </defs>
      </svg>
    </div>
  );
}
