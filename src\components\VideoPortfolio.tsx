"use client";

import { motion } from "framer-motion";
import { useState } from "react";

const videos = [
  {
    title: "UI Animation Showcase",
    description: "A collection of smooth UI animations and transitions",
    thumbnail: "https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&auto=format&fit=crop",
    videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ", // Example video embed
    category: "UI/UX",
    duration: "2:30",
  },
  {
    title: "E-commerce Demo",
    description: "Walk-through of a full-stack e-commerce platform",
    thumbnail: "https://images.unsplash.com/photo-1557821552-17105176677c?w=800&auto=format&fit=crop",
    videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    category: "Full Stack",
    duration: "3:45",
  },
  {
    title: "Portfolio Highlights",
    description: "Overview of recent web development projects",
    thumbnail: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&auto=format&fit=crop",
    videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    category: "Showcase",
    duration: "4:15",
  },
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export default function VideoPortfolio() {
  const [activeVideo, setActiveVideo] = useState<string | null>(null);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <motion.div
        className="text-center mb-12"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
      >
        <h2 className="text-3xl sm:text-4xl font-bold mb-4 gradient-text">Video Portfolio</h2>
        <p className="text-foreground/80">Watch demonstrations of my work in action</p>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {videos.map((video) => (
          <motion.div
            key={video.title}
            variants={itemVariants}
            className="group relative"
            onClick={() => setActiveVideo(video.videoUrl)}
          >
            <div className="relative aspect-video rounded-xl overflow-hidden cursor-pointer">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 opacity-0 group-hover:opacity-100 transition-opacity z-10" />
              <img
                src={video.thumbnail}
                alt={video.title}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-primary/30 group-hover:bg-primary/40 transition-colors">
                <div className="absolute inset-0 flex items-center justify-center">
                  <motion.div
                    className="w-16 h-16 rounded-full bg-accent/90 flex items-center justify-center"
                    whileHover={{ scale: 1.1 }}
                  >
                    <svg
                      className="w-8 h-8 text-background"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M8 5v14l11-7z" />
                    </svg>
                  </motion.div>
                </div>
              </div>
              <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-primary/80 to-transparent">
                <h3 className="text-white font-bold mb-1">{video.title}</h3>
                <div className="flex items-center gap-2 text-white/80 text-sm">
                  <span className="text-accent">{video.category}</span>
                  <span>•</span>
                  <span>{video.duration}</span>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {activeVideo && (
        <motion.div
          className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          onClick={() => setActiveVideo(null)}
        >
          <motion.div
            className="relative w-full max-w-4xl aspect-video bg-background rounded-xl overflow-hidden border border-accent/20"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            onClick={(e) => e.stopPropagation()}
          >
            <iframe
              src={activeVideo}
              className="w-full h-full"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
            <button
              className="absolute top-4 right-4 text-accent hover:text-accent-light transition-colors"
              onClick={() => setActiveVideo(null)}
            >
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
              </svg>
            </button>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}