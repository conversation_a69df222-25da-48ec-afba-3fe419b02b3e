"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import { useState } from "react";

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Creative Director at DesignCo",
    image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=400&fit=crop&crop=face",
    content: "Working with <PERSON> has been an absolute pleasure. Her attention to detail and creative vision brought our project to life in ways we couldn't have imagined.",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Founder of TechStart",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
    content: "<PERSON>'s ability to translate complex ideas into beautiful, intuitive designs is remarkable. She's not just a designer; she's a problem solver.",
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "Marketing Manager at CreativeHub",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face",
    content: "The level of professionalism and creativity Emma brings to each project is outstanding. She consistently delivers beyond expectations.",
  },
];

export default function Testimonials() {
  const [activeIndex, setActiveIndex] = useState(0);

  return (
    <div className="bg-white/5 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 via-red-500 to-purple-600 text-transparent bg-clip-text mb-4">
            Client Testimonials
          </h2>
          <p className="text-white/60 max-w-2xl mx-auto">
            Don't just take my word for it. Here's what some of my clients have to say about working together.
          </p>
        </motion.div>

        <div className="relative">
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                className="relative bg-black rounded-xl p-6 border border-white/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 * index }}
                whileHover={{ y: -5 }}
              >
                <div className="absolute -top-4 left-6">
                  <svg className="h-8 w-8 text-purple-600 transform rotate-180" fill="currentColor" viewBox="0 0 32 32">
                    <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                  </svg>
                </div>

                <div className="relative">
                  <div className="mt-6">
                    <p className="text-white/70 mb-4">{testimonial.content}</p>
                    <div className="flex items-center">
                      <div className="relative h-10 w-10 rounded-full overflow-hidden">
                        <Image
                          src={testimonial.image}
                          alt={testimonial.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-semibold text-white">{testimonial.name}</p>
                        <p className="text-sm text-white/60">{testimonial.role}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
