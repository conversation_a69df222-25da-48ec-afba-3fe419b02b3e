"use client";

import { motion } from 'framer-motion';
import Image from 'next/image';

const personalInfo = {
  name: "<PERSON>",
  Email: "<EMAIL>",
  Phone: "+*********** 5551",
  City: "Calabar, Nigeria",
};

const skills = [
  "Frontend Development (React, Next.js)",
  "UI/UX Design",
  "Animation & Motion Design",
  "Backend Development (Node.js)"
];

const experience = [
  {
    role: "Founder Of Trex Media",
    company: "Trex Media",
    period: "2024 - Present"
  },
  {
    role: "UI/UX Designer",
    company: "DesignStudio",
    period: "2021 - Present"
  }
];

const tools = [
  { name: "React/Next.js", level: 95 },
  { name: "TypeScript", level: 90 },
  { name: "Tailwind CSS", level: 92 },
  { name: "Framer Motion", level: 88 },
  { name: "Node.js", level: 85 },
];

export default function AboutPage() {
  return (
    <div className="min-h-screen pt-24 pb-16 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Personal Info and Bio Section */}
        <motion.div 
          className="grid grid-cols-1 lg:grid-cols-2 gap-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Personal Info Section */}
          <div>
            <motion.h2 
              className="text-3xl font-bold bg-gradient-to-r from-[#facc15] via-[#ef4444] to-[#9333ea] bg-clip-text text-transparent mb-6"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              Personal Info
            </motion.h2>
            
            <div className="space-y-4 text-white/70">
              {Object.entries(personalInfo).map(([key, value]) => (
                <div key={key} className="flex items-center space-x-2">
                  <span className="text-white capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                  <span>{value}</span>
                </div>
              ))}
            </div>

            <motion.div 
              className="mt-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              <a 
                href="/cv.pdf" 
                download
                className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-yellow-400 via-red-500 to-purple-600 text-white font-semibold hover:shadow-lg hover:shadow-purple-500/30 transition-shadow"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download CV
              </a>
            </motion.div>
          </div>

          {/* Bio Text Section */}
          <div>
            <motion.p 
              className="text-lg text-white/70 leading-relaxed mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              I started my journey as a designer with a passion for creating beautiful user experiences. 
              Over the years, I've evolved into a full-stack developer, combining my design background 
              with technical expertise to create engaging web applications that not only look great but 
              also provide exceptional user experiences.
              <br /><br />
              When I'm not coding, you can find me exploring new design trends, contributing to open-source 
              projects, or sharing my knowledge through technical writing and mentoring.
            </motion.p>
          </div>
        </motion.div>

        {/* Skills and Experience Section */}
        <motion.div 
          className="mt-16 grid grid-cols-1 lg:grid-cols-2 gap-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          {/* Skills */}
          <div>
            <h3 className="text-2xl font-semibold text-white mb-6">Skills</h3>
            <ul className="space-y-3">
              {skills.map((skill, index) => (
                <li key={index} className="flex items-center space-x-2">
                  <span className="text-yellow-400">▹</span>
                  <span className="text-white/70">{skill}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Experience */}
          <div>
            <h3 className="text-2xl font-semibold text-white mb-6">Experience</h3>
            <div className="space-y-6">
              {experience.map((exp, index) => (
                <div key={index}>
                  <h4 className="text-xl font-semibold text-white">{exp.role}</h4>
                  <p className="text-white/70">{exp.company} • {exp.period}</p>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Journey Section */}
        <motion.div
          className="mt-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <h3 className="text-2xl font-semibold text-white mb-6">My Journey</h3>
          <p className="text-white/70 leading-relaxed">
            I started my journey as a designer with a passion for creating beautiful user experiences. Over the years, I've evolved into a 
            full-stack developer, combining my design background with technical expertise to create engaging web applications that not 
            only look great but also provide exceptional user experiences.
          </p>
          <p className="text-white/70 leading-relaxed mt-4">
            When I'm not coding, you can find me exploring new design trends, contributing to open-source projects, or sharing my 
            knowledge through technical writing and mentoring.
          </p>
        </motion.div>

        {/* Remove Tools Section */}

        {/* Showreel Section */}
        <motion.div 
          className="mt-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}  // Updated delay since we removed Tools section
        >
          <h3 className="text-2xl font-semibold text-white mb-6">Development Showreel</h3>
          <div className="relative aspect-video rounded-xl overflow-hidden bg-white/5">
            <iframe
              src="https://www.youtube.com/embed/dQw4w9WgXcQ"
              title="Development Showreel"
              className="absolute inset-0 w-full h-full"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </div>
        </motion.div>

        {/* Portfolio Images */}
        <motion.div 
          className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}  // Updated delay since we removed Tools section
        >
          <div className="relative h-[400px] rounded-xl overflow-hidden">
            <Image
              src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800"
              alt="Working on development"
              fill
              className="object-cover"
            />
          </div>
          <div className="relative h-[400px] rounded-xl overflow-hidden">
            <Image
              src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800"
              alt="Coding workspace"
              fill
              className="object-cover"
            />
          </div>
        </motion.div>
      </div>
    </div>
  );
}