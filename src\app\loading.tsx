import { motion } from 'framer-motion';

export default function Loading() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <motion.div
        className="relative"
        animate={{ rotate: 360 }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "linear"
        }}
      >
        {/* Outer ring */}
        <div className="w-16 h-16 rounded-full border-4 border-primary/20" />
        
        {/* Spinner gradient */}
        <div className="absolute top-0 left-0 w-16 h-16 rounded-full border-4 border-t-accent border-r-accent/50 border-b-transparent border-l-transparent" />
        
        {/* Inner glow */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-primary/10 to-accent/10 blur-sm" />
      </motion.div>
    </div>
  );
}