const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and TSX files
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !filePath.includes('node_modules') && !filePath.includes('.next')) {
      fileList = findTsFiles(filePath, fileList);
    } else if (
      (file.endsWith('.ts') || file.endsWith('.tsx')) && 
      !file.endsWith('.d.ts')
    ) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to fix common ESLint issues
function fixEslintIssues(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix unescaped entities
  const entityReplacements = [
    { pattern: /(\s)'(\s)/g, replacement: "$1'$2" },
    { pattern: /(\s)"(\s)/g, replacement: '$1"$2' }
  ];

  entityReplacements.forEach(({ pattern, replacement }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      modified = true;
    }
  });

  // Save the file if modified
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed issues in: ${filePath}`);
  }
}

// Main execution
const rootDir = process.cwd();
const tsFiles = findTsFiles(rootDir);

console.log(`Found ${tsFiles.length} TypeScript files to process`);

tsFiles.forEach(file => {
  try {
    fixEslintIssues(file);
  } catch (error) {
    console.error(`Error processing ${file}:`, error);
  }
});

console.log('Finished processing files');
