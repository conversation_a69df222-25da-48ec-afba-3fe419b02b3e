"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ProjectCard from '@/components/projects/ProjectCard';
import ProjectFilter from '@/components/projects/ProjectFilter';

// Sample project data - replace with your actual projects
const projects = [
  {
    id: 1,
    title: "E-commerce Platform",
    description: "A modern e-commerce platform built with Next.js and Stripe integration.",
    image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop",
    category: "Web Development",
    technologies: ["Next.js", "React", "Stripe", "Tailwind CSS"],
    link: "https://example.com/ecommerce",
    github: "https://github.com/yourusername/ecommerce"
  },
  {
    id: 2,
    title: "Portfolio Website",
    description: "A creative portfolio website with smooth animations and responsive design.",
    image: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=800&h=600&fit=crop",
    category: "UI/UX Design",
    technologies: ["React", "Framer Motion", "Tailwind CSS"],
    link: "https://example.com/portfolio",
    github: "https://github.com/yourusername/portfolio"
  },
  {
    id: 3,
    title: "Task Management App",
    description: "A productivity app for managing tasks and projects with team collaboration features.",
    image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop",
    category: "Mobile App",
    technologies: ["React Native", "Firebase", "Redux"],
    link: "https://example.com/taskapp",
    github: "https://github.com/yourusername/taskapp"
  },
  {
    id: 4,
    title: "AI Image Generator",
    description: "An application that generates images using AI based on text prompts.",
    image: "https://images.unsplash.com/photo-*************-21780ecad995?w=800&h=600&fit=crop",
    category: "AI/ML",
    technologies: ["Python", "TensorFlow", "React", "Flask"],
    link: "https://example.com/ai-generator",
    github: "https://github.com/yourusername/ai-generator"
  },
  {
    id: 5,
    title: "Social Media Dashboard",
    description: "A comprehensive dashboard for managing and analyzing social media accounts.",
    image: "https://images.unsplash.com/photo-*************-afdab827c52f?w=800&h=600&fit=crop",
    category: "Web Development",
    technologies: ["Vue.js", "D3.js", "Node.js", "Express"],
    link: "https://example.com/social-dashboard",
    github: "https://github.com/yourusername/social-dashboard"
  },
  {
    id: 6,
    title: "Fitness Tracker",
    description: "A mobile app for tracking workouts, nutrition, and fitness progress.",
    image: "https://images.unsplash.com/photo-*************-1cb2f99b2d8b?w=800&h=600&fit=crop",
    category: "Mobile App",
    technologies: ["Flutter", "Firebase", "Google Fit API"],
    link: "https://example.com/fitness",
    github: "https://github.com/yourusername/fitness"
  }
];

const categories = ["All", "Web Development", "UI/UX Design", "Mobile App", "AI/ML"];

export default function ProjectsPage() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [filteredProjects, setFilteredProjects] = useState(projects);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Filter projects based on selected category
    if (selectedCategory === "All") {
      setFilteredProjects(projects);
    } else {
      setFilteredProjects(projects.filter(project => project.category === selectedCategory));
    }
  }, [selectedCategory]);

  useEffect(() => {
    // Set loaded state after a small delay to trigger animations
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <motion.div 
      className="min-h-screen pt-24 pb-16 relative overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Background elements */}
      <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-[#ff8a00]/10 to-transparent pointer-events-none" />
      <div className="absolute bottom-0 right-0 w-full h-64 bg-gradient-to-t from-[#d946ef]/10 to-transparent pointer-events-none" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-12"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 100 }}
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-4 gradient-text">My Projects</h1>
          <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
            Explore my latest work across various domains including web development, 
            UI/UX design, and mobile applications.
          </p>
        </motion.div>
        
        <motion.div 
          className="mb-12"
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 100 }}
        >
          <ProjectFilter 
            categories={categories} 
            selectedCategory={selectedCategory} 
            onSelectCategory={setSelectedCategory} 
          />
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project, index) => (
            <ProjectCard 
              key={project.id} 
              project={project} 
              index={index}
              isLoaded={isLoaded}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
}