@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #000000;
  --foreground: #ffffff;
  --primary: #ff8a00;
  --primary-light: #ffa033;
  --primary-dark: #e67a00;
  --accent: #d946ef;
  --accent-light: #e879f9;
  --accent-dark: #c026d3;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-geist-sans);
}

/* Gradient text utility */
.gradient-text {
  @apply bg-gradient-to-r from-[#ff8a00] to-[#d946ef] bg-clip-text text-transparent;
}
