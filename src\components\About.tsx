"use client";

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

export default function About() {
  return (
    <div id="about" className="py-24 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute top-0 right-0 w-full h-64 bg-gradient-to-b from-[#ff8a00]/10 to-transparent pointer-events-none" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ y: 30, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          viewport={{ once: true }}
          transition={{ type: "spring", stiffness: 100 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 gradient-text">About Me</h2>
          <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
            I'm a passionate developer dedicated to creating seamless digital experiences.
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Image Section */}
          <motion.div
            className="relative"
            initial={{ x: -50, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ type: "spring", stiffness: 100 }}
          >
            <div className="relative h-[450px] w-full rounded-2xl overflow-hidden">
              <Image
                src="/profile.jpg"
                alt="Jackson Abetianbe"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            </div>
            
            {/* Experience Cards */}
            <motion.div
              className="absolute -bottom-6 -right-6 bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10 shadow-xl"
              initial={{ scale: 0.8, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3 }}
            >
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 rounded-full bg-gradient-to-r from-[#ff8a00] to-[#d946ef] flex items-center justify-center">
                  <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-foreground/70">Experience</p>
                  <p className="text-xl font-bold">3+ Years</p>
                </div>
              </div>
            </motion.div>
            
            <motion.div
              className="absolute -top-6 -left-6 bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10 shadow-xl"
              initial={{ scale: 0.8, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4 }}
            >
              <div className="flex items-center gap-4">
                <div className="h-12 w-12 rounded-full bg-gradient-to-r from-[#ff8a00] to-[#d946ef] flex items-center justify-center">
                  <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-foreground/70">Completed</p>
                  <p className="text-xl font-bold">20+ Projects</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
          
          {/* Content Section */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ type: "spring", stiffness: 100 }}
          >
            <h3 className="text-2xl font-bold mb-4">
              A creative web developer and designer dedicated to creating seamless digital experiences
            </h3>
            
            <p className="text-foreground/80 mb-6">
              My passion for technology and design allows me to create user-friendly interfaces that engage and inspire. I specialize in building responsive, accessible websites and applications that not only look great but also perform exceptionally well.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <div className="flex items-center gap-2">
                <svg className="h-5 w-5 text-[#ff8a00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Web Development</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="h-5 w-5 text-[#ff8a00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>UI/UX Design</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="h-5 w-5 text-[#ff8a00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Responsive Design</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="h-5 w-5 text-[#ff8a00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Frontend Development</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="h-5 w-5 text-[#ff8a00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Backend Development</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="h-5 w-5 text-[#ff8a00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Mobile-First Approach</span>
              </div>
            </div>
            
            <div className="flex flex-wrap gap-4">
              <Link 
                href="#contact" 
                className="px-6 py-3 rounded-full bg-gradient-to-r from-[#ff8a00] to-[#d946ef] text-white font-medium hover:shadow-lg hover:shadow-[#d946ef]/20 transition-all"
              >
                Contact Me
              </Link>
              
              <Link 
                href="/resume.pdf" 
                className="px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm text-white font-medium hover:bg-white/20 transition-all flex items-center gap-2"
                target="_blank"
              >
                Download CV
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
              </Link>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
