"use client";

import { motion } from "framer-motion";
import Image from "next/image";

const personalInfo = {
  name: "<PERSON>",
  Email: "<EMAIL>",
  Phone: "+234 ************",
  City: "Calabar, Nigeria",
};

const skills = [
  { category: "Frontend Development (React, Next.js)", level: 95 },
  { category: "UI/UX Design", level: 90 },
  { category: "Animation & Motion Design", level: 85 },
  { category: "Backend Development (Node.js)", level: 88 },
];

const tools = [
  { name: "React/Next.js", level: 95 },
  { name: "TypeScript", level: 90 },
  { name: "Tailwind CSS", level: 92 },
  { name: "Framer Motion", level: 88 },
  { name: "Node.js", level: 85 },
];

export default function Biography() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <motion.div 
        className="grid grid-cols-1 lg:grid-cols-2 gap-12"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Personal Info Section */}
        <div>
          <motion.h2 
            className="text-3xl font-bold gradient-text mb-6"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            Personal Info
          </motion.h2>
          
          <div className="space-y-4 text-foreground/70">
            {Object.entries(personalInfo).map(([key, value]) => (
              <div key={key} className="flex items-center space-x-2">
                <span className="gradient-text capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                <span>{value}</span>
              </div>
            ))}
          </div>

          <motion.div 
            className="mt-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            <a 
              href="/cv.pdf" 
              download
              className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-[#ff8a00] to-[#d946ef] text-white font-semibold hover:shadow-lg hover:shadow-[#d946ef]/20 transition-all"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              Download CV
            </a>
          </motion.div>
        </div>

        {/* Bio Text Section */}
        <div>
          <motion.p 
            className="text-lg text-foreground/80 leading-relaxed mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            I started my journey as a designer with a passion for creating beautiful user experiences. 
            Over the years, I've evolved into a full-stack developer, combining my design background 
            with technical expertise to create engaging web applications that not only look great but 
            also provide exceptional user experiences.
            <br /><br />
            When I'm not coding, you can find me exploring new design trends, contributing to open-source 
            projects, or sharing my knowledge through technical writing and mentoring.
          </motion.p>
        </div>
      </motion.div>

      {/* Showreel Section */}
      <motion.div 
        className="mt-16"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <h3 className="text-2xl font-semibold text-white mb-6">Development Showreel</h3>
        <div className="relative aspect-video rounded-xl overflow-hidden bg-white/5">
          <iframe
            src="https://www.youtube.com/embed/dQw4w9WgXcQ"
            title="Development Showreel"
            className="absolute inset-0 w-full h-full"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        </div>
      </motion.div>

      {/* Skills Section */}
      <motion.div 
        className="mt-16 grid grid-cols-1 lg:grid-cols-2 gap-12"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        {/* Main Skills */}
        <div>
          <h3 className="text-2xl font-semibold gradient-text mb-6">Main Skills</h3>
          <div className="space-y-4">
            {skills.map((skill) => (
              <div key={skill.category}>
                <div className="flex justify-between mb-2">
                  <span className="text-foreground/80">{skill.category}</span>
                  <span className="gradient-text">{skill.level}%</span>
                </div>
                <div className="h-2 bg-primary/10 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-[#ff8a00] to-[#d946ef]"
                    initial={{ width: 0 }}
                    whileInView={{ width: `${skill.level}%` }}
                    viewport={{ once: true }}
                    transition={{ duration: 1, ease: "easeOut" }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Tools & Software */}
        <div>
          <h3 className="text-2xl font-semibold gradient-text mb-6">Tools & Software</h3>
          <div className="space-y-4">
            {tools.map((tool) => (
              <div key={tool.name}>
                <div className="flex justify-between mb-2">
                  <span className="text-foreground/80">{tool.name}</span>
                  <span className="gradient-text">{tool.level}%</span>
                </div>
                <div className="h-2 bg-primary/10 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-[#ff8a00] to-[#d946ef]"
                    initial={{ width: 0 }}
                    whileInView={{ width: `${tool.level}%` }}
                    viewport={{ once: true }}
                    transition={{ duration: 1, ease: "easeOut" }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Portfolio Images */}
      <motion.div 
        className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <div className="relative h-[400px] rounded-xl overflow-hidden group">
          <Image
            src="/about/portrait.jpg"
            alt="Working on illustration"
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-tr from-[#ff8a00]/40 to-[#d946ef]/40 opacity-0 group-hover:opacity-100 transition-opacity" />
        </div>
        <div className="relative h-[400px] rounded-xl overflow-hidden group">
          <Image
            src="/about/working.jpg"
            alt="Creating artwork"
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-tr from-[#ff8a00]/40 to-[#d946ef]/40 opacity-0 group-hover:opacity-100 transition-opacity" />
        </div>
      </motion.div>
    </div>
  );
}
