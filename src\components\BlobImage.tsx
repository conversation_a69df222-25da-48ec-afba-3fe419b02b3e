"use client";

import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useState, useCallback, useId } from 'react';

interface BlobImageProps {
  src: string;
  alt?: string;
  className?: string;
  width?: number;
  height?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export default function BlobImage({
  src = '',
  alt = "",
  className = "",
  width,
  height,
  maxWidth,
  maxHeight
}: BlobImageProps) {
  const [blobPath, setBlobPath] = useState("");
  const [isHovered, setIsHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const uniqueId = useId();
  const gooId = `goo-${uniqueId}`;
  const gradientId = `gradient-${uniqueId}`;

  // Generate blob points
  const generateBlobPath = useCallback((seed: number) => {
    const points = 8;
    const slice = (Math.PI * 2) / points;
    const startAngle = slice / 2;
    const radius = 50;
    const randomRange = 10;
    interface Point {
      x: number;
      y: number;
    }

    const blobPoints: Point[] = [];

    for (let i = 0; i < points; i++) {
      const angle = startAngle + (i * slice);
      const randRadius = radius + (Math.cos(seed + i) * randomRange);
      const x = randRadius * Math.cos(angle);
      const y = randRadius * Math.sin(angle);
      blobPoints.push({ x, y });
    }

    const svgPath = blobPoints.reduce((path, point, index) => {
      if (index === 0) {
        return `M ${point.x} ${point.y}`;
      }
      const prevPoint = blobPoints[index - 1];
      const controlPoint1 = {
        x: prevPoint.x + (point.x - prevPoint.x) * 0.5,
        y: prevPoint.y + (point.y - prevPoint.y) * 0.5
      };
      const controlPoint2 = {
        x: point.x - (point.x - prevPoint.x) * 0.5,
        y: point.y - (point.y - prevPoint.y) * 0.5
      };
      return `${path} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${point.x} ${point.y}`;
    }, "");

    return svgPath + " Z";
  }, []);

  useEffect(() => {
    let seed = 0;
    let animationFrameId: number;
    let isAnimating = true;

    const updatePath = () => {
      if (!isAnimating) return;
      seed += 0.02;
      setBlobPath(generateBlobPath(seed));
      animationFrameId = requestAnimationFrame(updatePath);
    };

    animationFrameId = requestAnimationFrame(updatePath);

    return () => {
      isAnimating = false;
      cancelAnimationFrame(animationFrameId);
    };
  }, [generateBlobPath]);

  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const containerStyle = {
    width: width ? `${width}px` : '100%',
    height: height ? `${height}px` : '100%',
    maxWidth: maxWidth ? `${maxWidth}px` : undefined,
    maxHeight: maxHeight ? `${maxHeight}px` : undefined,
  };

  if (!src) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-800 rounded-full ${className}`}
        style={containerStyle}
      >
        <span className="text-white">No image</span>
      </div>
    );
  }

  if (hasError) {
    return (
      <div
        className={`flex flex-col items-center justify-center bg-gray-800 rounded-full ${className}`}
        style={containerStyle}
      >
        <p className="text-white">Error loading image</p>
        <button
          onClick={() => {
            setIsLoading(true);
            setHasError(false);
          }}
          className="mt-2 px-4 py-2 bg-purple-500 text-white rounded-full hover:bg-purple-600 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div
      className={`relative ${className}`}
      style={containerStyle}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      role="img"
      aria-label={alt}
    >
      <svg
        viewBox="-60 -60 120 120"
        className="absolute w-full h-full"
        style={{ filter: `url(#${gooId})` }}
        aria-hidden="true"
      >
        <defs>
          <filter id={gooId}>
            <feGaussianBlur in="SourceGraphic" stdDeviation="2" result="blur" />
            <feColorMatrix
              in="blur"
              mode="matrix"
              values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
              result="goo"
            />
          </filter>
          <linearGradient id={gradientId} gradientTransform="rotate(45)">
            <stop offset="0%" stopColor="var(--primary)" />
            <stop offset="50%" stopColor="var(--accent)" />
            <stop offset="100%" stopColor="var(--primary-light)" />
          </linearGradient>
        </defs>
        <motion.path
          d={blobPath}
          fill="none"
          stroke={`url(#${gradientId})`}
          strokeWidth="1"
          animate={{
            scale: isHovered ? 1.05 : 1,
          }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 20
          }}
        />
      </svg>

      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center bg-primary/5 rounded-full"
          >
            <div className="w-8 h-8 border-4 border-t-transparent border-accent rounded-full animate-spin" />
          </motion.div>
        )}
      </AnimatePresence>

      <div className="absolute inset-[5%] rounded-full overflow-hidden">
        <motion.img
          src={src}
          alt={alt}
          className="w-full h-full object-cover"
          animate={{
            scale: isHovered ? 1.1 : 1,
          }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 20
          }}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      </div>
    </div>
  );
}
