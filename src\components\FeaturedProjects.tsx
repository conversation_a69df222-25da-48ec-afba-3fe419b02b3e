"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import ProjectCard from './projects/ProjectCard';

// Featured projects - you can replace these with your actual featured projects
const featuredProjects = [
  {
    id: 1,
    title: "E-commerce Platform",
    description: "A modern e-commerce platform built with Next.js and Stripe integration.",
    image: "/projects/ecommerce.jpg",
    category: "Web Development",
    technologies: ["Next.js", "React", "Stripe", "Tailwind CSS"],
    link: "https://example.com/ecommerce",
    github: "https://github.com/yourusername/ecommerce"
  },
  {
    id: 2,
    title: "Portfolio Website",
    description: "A creative portfolio website with smooth animations and responsive design.",
    image: "/projects/portfolio.jpg",
    category: "UI/UX Design",
    technologies: ["React", "Framer Motion", "Tailwind CSS"],
    link: "https://example.com/portfolio",
    github: "https://github.com/yourusername/portfolio"
  },
  {
    id: 3,
    title: "Task Management App",
    description: "A productivity app for managing tasks and projects with team collaboration features.",
    image: "/projects/taskapp.jpg",
    category: "Mobile App",
    technologies: ["React Native", "Firebase", "Redux"],
    link: "https://example.com/taskapp",
    github: "https://github.com/yourusername/taskapp"
  }
];

export default function FeaturedProjects() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Set loaded state after a small delay to trigger animations
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="py-24 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-[#ff8a00]/10 to-transparent pointer-events-none" />
      <div className="absolute bottom-0 right-0 w-full h-64 bg-gradient-to-t from-[#d946ef]/10 to-transparent pointer-events-none" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-12"
          initial={{ y: 30, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          viewport={{ once: true }}
          transition={{ type: "spring", stiffness: 100 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 gradient-text">Featured Projects</h2>
          <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
            Check out some of my recent work. These projects showcase my skills and expertise in web development and design.
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredProjects.map((project, index) => (
            <ProjectCard 
              key={project.id} 
              project={project} 
              index={index}
              isLoaded={isLoaded}
            />
          ))}
        </div>
        
        <motion.div 
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
        >
          <Link 
            href="/projects" 
            className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-[#ff8a00] to-[#d946ef] text-white font-medium hover:shadow-lg hover:shadow-[#d946ef]/20 transition-all"
          >
            View All Projects
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </motion.div>
      </div>
    </div>
  );
}
