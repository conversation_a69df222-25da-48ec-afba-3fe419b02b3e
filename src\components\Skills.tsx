"use client";

import { motion } from 'framer-motion';
import { SiReact, SiTypescript, SiTailwindcss, SiNextdotjs, SiFigma, SiAdobexd, SiNodedotjs, SiGit } from 'react-icons/si';

const skills = [
  {
    category: "Frontend Development",
    items: [
      { name: "React", icon: SiReact, level: 90 },
      { name: "TypeScript", icon: SiTypescript, level: 85 },
      { name: "Tailwind CSS", icon: SiTailwindcss, level: 95 },
      { name: "Next.js", icon: SiNextdotjs, level: 88 },
    ]
  },
  {
    category: "UI/UX Design",
    items: [
      { name: "Figma", icon: SiFigma, level: 92 },
      { name: "Adobe XD", icon: SiAdobexd, level: 85 },
    ]
  },
  {
    category: "Backend & Tools",
    items: [
      { name: "Node.js", icon: SiNodedotjs, level: 80 },
      { name: "Git", icon: SiGit, level: 88 },
    ]
  }
];

export default function Skills() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <motion.h2 
        className="text-4xl font-bold text-center mb-16 gradient-text"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
      >
        Skills & Expertise
      </motion.h2>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {skills.map((category, idx) => (
          <motion.div
            key={category.category}
            className="bg-primary/5 rounded-xl p-6 backdrop-blur-sm border border-primary/10 hover:border-accent/20 transition-all"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: idx * 0.1 }}
          >
            <h3 className="text-xl font-semibold mb-6 text-accent">{category.category}</h3>
            <div className="space-y-6">
              {category.items.map((skill) => (
                <div key={skill.name} className="space-y-2">
                  <div className="flex items-center gap-2 text-foreground/80">
                    <skill.icon className="w-5 h-5 text-primary" />
                    <span>{skill.name}</span>
                  </div>
                  <div className="h-2 bg-primary/10 rounded-full overflow-hidden">
                    <motion.div
                      className="h-full bg-gradient-to-r from-primary via-accent to-primary-light"
                      initial={{ width: 0 }}
                      whileInView={{ width: `${skill.level}%` }}
                      viewport={{ once: true }}
                      transition={{ duration: 1, ease: "easeOut" }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
