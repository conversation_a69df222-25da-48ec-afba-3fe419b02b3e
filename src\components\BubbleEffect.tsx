"use client";

import { motion } from 'framer-motion';

interface BubbleEffectProps {
  className?: string;
}

export default function BubbleEffect({ className = '' }: BubbleEffectProps) {
  // Generate random bubbles
  const bubbleCount = 15;
  const bubbles = Array.from({ length: bubbleCount }).map((_, i) => ({
    id: i,
    size: Math.random() * 100 + 50,
    x: Math.random() * 100,
    y: Math.random() * 100,
    duration: Math.random() * 20 + 10,
    delay: Math.random() * -20,
    // Randomly choose between primary and accent colors
    color: Math.random() > 0.5 ? 'primary' : 'accent'
  }));

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {bubbles.map(bubble => (
        <motion.div
          key={bubble.id}
          className={`absolute rounded-full backdrop-blur-sm ${
            bubble.color === 'primary'
              ? 'bg-primary/10 border border-primary/20'
              : 'bg-accent/10 border border-accent/20'
          }`}
          style={{
            width: bubble.size,
            height: bubble.size,
            left: `${bubble.x}%`,
            top: `${bubble.y}%`,
          }}
          animate={{
            x: [0, 30, 0],
            y: [0, -30, 0],
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: bubble.duration,
            repeat: Infinity,
            delay: bubble.delay,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
}
