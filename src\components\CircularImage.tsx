"use client";

import { motion } from 'framer-motion';

interface CircularImageProps {
  src: string;
  alt?: string;
  className?: string;
}

export default function CircularImage({ src, alt = "", className = "" }: CircularImageProps) {
  return (
    <div className={`relative ${className}`}>
      {/* Outer glow/gradient ring */}
      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 via-accent/20 to-primary/20 blur-xl" />
      
      {/* Image container */}
      <motion.div
        className="relative w-full h-full rounded-full overflow-hidden border-4 border-accent/10"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        whileHover={{ scale: 1.02 }}
      >
        {/* Background blur effect */}
        <div className="absolute inset-0 bg-gradient-to-b from-primary/5 to-accent/20 backdrop-blur-[2px]" />
        
        {/* The image */}
        <img
          src={src}
          alt={alt}
          className="w-full h-full object-cover"
        />

        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-primary/40 via-transparent to-transparent" />
      </motion.div>
    </div>
  );
}
